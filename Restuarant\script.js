// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    
    mobileMenuBtn.addEventListener('click', function() {
        mobileMenu.classList.toggle('hidden');
    });

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 64; // Account for fixed header
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
                
                // Close mobile menu if open
                mobileMenu.classList.add('hidden');
            }
        });
    });

    // Menu functionality
    const menuFilters = document.querySelectorAll('.menu-filter');
    const menuContainer = document.getElementById('menu-items');
    let allMenuItems = [];

    // Load menu items from backend
    async function loadMenuItems(category = 'all') {
        try {
            const response = await restaurantAPI.getMenu(category);
            allMenuItems = response.data;
            renderMenuItems(allMenuItems);
        } catch (error) {
            console.warn('Failed to load menu from API, using static menu');
            // Fallback to static menu items
            initializeStaticMenu();
        }
    }

    // Render menu items in the DOM
    function renderMenuItems(items) {
        if (!menuContainer) return;

        menuContainer.innerHTML = '';

        items.forEach((item, index) => {
            const menuItemHTML = `
                <div class="menu-item bg-white p-6 rounded-lg shadow-sm" data-category="${item.category}" style="animation-delay: ${index * 0.1}s">
                    <div class="flex justify-between items-start mb-3">
                        <h3 class="text-xl font-serif font-semibold text-charcoal">${item.name}</h3>
                        <span class="text-gold font-bold text-lg">${APIUtils.formatCurrency(item.price)}</span>
                    </div>
                    <p class="text-gray-600 mb-2">${item.description}</p>
                    <div class="flex space-x-2">
                        ${item.dietary ? item.dietary.map(tag => `
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">${tag}</span>
                        `).join('') : ''}
                    </div>
                </div>
            `;
            menuContainer.innerHTML += menuItemHTML;
        });

        // Re-initialize menu items for filtering
        initializeMenuFiltering();
    }

    // Initialize static menu (fallback)
    function initializeStaticMenu() {
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.style.display = '';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
            item.style.transition = 'all 0.3s ease';
        });
        initializeMenuFiltering();
    }

    // Initialize menu filtering
    function initializeMenuFiltering() {
        const menuItems = document.querySelectorAll('.menu-item');

        menuItems.forEach(item => {
            item.style.display = '';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
            item.style.transition = 'all 0.3s ease';
        });
    }

    // Menu filter event listeners
    menuFilters.forEach(filter => {
        filter.addEventListener('click', async function() {
            const category = this.getAttribute('data-category');

            // Update active filter
            menuFilters.forEach(f => {
                f.classList.remove('active', 'bg-gold', 'text-white');
                f.classList.add('bg-white', 'text-charcoal');
            });
            this.classList.add('active', 'bg-gold', 'text-white');
            this.classList.remove('bg-white', 'text-charcoal');

            // Load and filter menu items
            if (allMenuItems.length > 0) {
                // Filter existing items
                const filteredItems = category === 'all' ? allMenuItems : allMenuItems.filter(item => item.category === category);
                renderMenuItems(filteredItems);
            } else {
                // Fallback to static filtering
                const menuItems = document.querySelectorAll('.menu-item');
                menuItems.forEach(item => {
                    const itemCategory = item.getAttribute('data-category');
                    if (category === 'all' || itemCategory === category) {
                        item.style.display = '';
                        item.style.opacity = '0';
                        item.style.transform = 'translateY(20px)';

                        setTimeout(() => {
                            item.style.opacity = '1';
                            item.style.transform = 'translateY(0)';
                        }, 50);
                    } else {
                        item.style.opacity = '0';
                        item.style.transform = 'translateY(-20px)';

                        setTimeout(() => {
                            item.style.display = 'none';
                        }, 300);
                    }
                });
            }
        });
    });

    // Load menu on page load
    loadMenuItems();

    // Reservation form handling
    const reservationForm = document.getElementById('reservation-form');
    reservationForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const submitButton = this.querySelector('button[type="submit"]');
        APIUtils.showLoading(submitButton);

        try {
            // Get form data
            const formData = new FormData(this);
            const reservationData = {
                name: formData.get('name'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                guests: formData.get('guests'),
                date: formData.get('date'),
                time: formData.get('time'),
                specialRequests: formData.get('special-requests')
            };

            // Client-side validation
            if (!reservationData.name || !reservationData.email || !reservationData.phone ||
                !reservationData.guests || !reservationData.date || !reservationData.time) {
                showNotification('Please fill in all required fields.', 'error');
                return;
            }

            // Validate email format
            if (!APIUtils.isValidEmail(reservationData.email)) {
                showNotification('Please enter a valid email address.', 'error');
                return;
            }

            // Validate phone format
            if (!APIUtils.isValidPhone(reservationData.phone)) {
                showNotification('Please enter a valid phone number.', 'error');
                return;
            }

            // Send reservation to backend
            const response = await restaurantAPI.createReservation(reservationData);

            // Show success message
            APIUtils.showAPINotification(response, 'success');

            // Reset form
            this.reset();

        } catch (error) {
            APIUtils.handleAPIError(error);
        } finally {
            APIUtils.hideLoading(submitButton);
        }
    });

    // Contact form handling
    const contactForm = document.getElementById('contact-form');
    contactForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const submitButton = this.querySelector('button[type="submit"]');
        APIUtils.showLoading(submitButton);

        try {
            // Get form data
            const formData = new FormData(this);
            const contactData = {
                name: formData.get('name'),
                email: formData.get('email'),
                subject: formData.get('subject'),
                message: formData.get('message')
            };

            // Client-side validation
            if (!contactData.name || !contactData.email || !contactData.subject || !contactData.message) {
                showNotification('Please fill in all required fields.', 'error');
                return;
            }

            // Validate email format
            if (!APIUtils.isValidEmail(contactData.email)) {
                showNotification('Please enter a valid email address.', 'error');
                return;
            }

            // Send message to backend
            const response = await restaurantAPI.sendContactMessage(contactData);

            // Show success message
            APIUtils.showAPINotification(response, 'success');

            // Reset form
            this.reset();

        } catch (error) {
            APIUtils.handleAPIError(error);
        } finally {
            APIUtils.hideLoading(submitButton);
        }
    });

    // Set minimum date for reservation form
    const dateInput = document.getElementById('date');
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    dateInput.min = tomorrow.toISOString().split('T')[0];

    // Navbar scroll effect
    const navbar = document.querySelector('nav');
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.classList.add('shadow-lg');
            navbar.classList.remove('shadow-sm');
        } else {
            navbar.classList.add('shadow-sm');
            navbar.classList.remove('shadow-lg');
        }
    });

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe sections for scroll animations
    const sections = document.querySelectorAll('section');
    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(section);
    });
});

// Utility function to show notifications
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification fixed top-20 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;
    
    // Set colors based on type
    if (type === 'success') {
        notification.classList.add('bg-green-500', 'text-white');
    } else if (type === 'error') {
        notification.classList.add('bg-red-500', 'text-white');
    } else {
        notification.classList.add('bg-blue-500', 'text-white');
    }

    notification.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .menu-item {
        transition: all 0.3s ease;
    }
    
    .menu-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    
    .nav-link {
        position: relative;
    }
    
    .nav-link::after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: -4px;
        left: 0;
        background-color: #D4AF37;
        transition: width 0.3s ease;
    }
    
    .nav-link:hover::after {
        width: 100%;
    }
`;
document.head.appendChild(style);
