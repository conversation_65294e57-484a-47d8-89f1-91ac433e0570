const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
    contentSecurityPolicy: false, // Disable for development
}));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname)));

// In-memory storage (replace with database in production)
let reservations = [];
let contactMessages = [];
let menuItems = [];
let events = [];

// Initialize sample data
const initializeData = () => {
    // Sample menu items
    menuItems = [
        {
            id: 1,
            name: "Burrata with Truffle Oil",
            category: "appetizers",
            price: 1950,
            description: "Fresh burrata cheese with black truffle oil, arugula, and toasted pine nuts",
            dietary: ["vegetarian", "gluten-free"],
            available: true
        },
        {
            id: 2,
            name: "Osso Buco alla Milanese",
            category: "mains",
            price: 3650,
            description: "Braised veal shanks with saffron risotto and gremolata",
            dietary: ["chef-special"],
            available: true
        },
        {
            id: 3,
            name: "Tiramisu",
            category: "desserts",
            price: 1150,
            description: "Classic Italian dessert with espresso-soaked ladyfingers and mascarpone",
            dietary: ["vegetarian"],
            available: true
        }
    ];

    // Sample events
    events = [
        {
            id: 1,
            title: "Wine Tasting Evening",
            description: "Join our sommelier for an exclusive wine tasting featuring rare vintages paired with artisanal cheeses.",
            price: 6850,
            date: "2024-12-15",
            time: "19:00",
            capacity: 20,
            booked: 5,
            type: "monthly"
        }
    ];
};

// API Routes

// Get all menu items
app.get('/api/menu', (req, res) => {
    const { category } = req.query;
    let filteredItems = menuItems.filter(item => item.available);
    
    if (category && category !== 'all') {
        filteredItems = filteredItems.filter(item => item.category === category);
    }
    
    res.json({
        success: true,
        data: filteredItems,
        count: filteredItems.length
    });
});

// Get single menu item
app.get('/api/menu/:id', (req, res) => {
    const item = menuItems.find(item => item.id === parseInt(req.params.id));
    
    if (!item) {
        return res.status(404).json({
            success: false,
            message: 'Menu item not found'
        });
    }
    
    res.json({
        success: true,
        data: item
    });
});

// Create reservation
app.post('/api/reservations', (req, res) => {
    const { name, email, phone, guests, date, time, specialRequests } = req.body;
    
    // Validation
    if (!name || !email || !phone || !guests || !date || !time) {
        return res.status(400).json({
            success: false,
            message: 'All required fields must be provided'
        });
    }
    
    // Check if date is in the future
    const reservationDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (reservationDate < today) {
        return res.status(400).json({
            success: false,
            message: 'Reservation date must be in the future'
        });
    }
    
    const reservation = {
        id: reservations.length + 1,
        name,
        email,
        phone,
        guests: parseInt(guests),
        date,
        time,
        specialRequests: specialRequests || '',
        status: 'pending',
        createdAt: new Date().toISOString()
    };
    
    reservations.push(reservation);
    
    res.status(201).json({
        success: true,
        message: 'Reservation created successfully',
        data: reservation
    });
});

// Get all reservations (admin endpoint)
app.get('/api/reservations', (req, res) => {
    res.json({
        success: true,
        data: reservations,
        count: reservations.length
    });
});

// Update reservation status
app.patch('/api/reservations/:id', (req, res) => {
    const { status } = req.body;
    const reservation = reservations.find(r => r.id === parseInt(req.params.id));
    
    if (!reservation) {
        return res.status(404).json({
            success: false,
            message: 'Reservation not found'
        });
    }
    
    if (status) {
        reservation.status = status;
        reservation.updatedAt = new Date().toISOString();
    }
    
    res.json({
        success: true,
        message: 'Reservation updated successfully',
        data: reservation
    });
});

// Create contact message
app.post('/api/contact', (req, res) => {
    const { name, email, subject, message } = req.body;
    
    // Validation
    if (!name || !email || !subject || !message) {
        return res.status(400).json({
            success: false,
            message: 'All fields are required'
        });
    }
    
    const contactMessage = {
        id: contactMessages.length + 1,
        name,
        email,
        subject,
        message,
        status: 'unread',
        createdAt: new Date().toISOString()
    };
    
    contactMessages.push(contactMessage);
    
    res.status(201).json({
        success: true,
        message: 'Message sent successfully',
        data: contactMessage
    });
});

// Get all contact messages (admin endpoint)
app.get('/api/contact', (req, res) => {
    res.json({
        success: true,
        data: contactMessages,
        count: contactMessages.length
    });
});

// Get all events
app.get('/api/events', (req, res) => {
    res.json({
        success: true,
        data: events,
        count: events.length
    });
});

// Book event
app.post('/api/events/:id/book', (req, res) => {
    const { name, email, phone, guests } = req.body;
    const event = events.find(e => e.id === parseInt(req.params.id));
    
    if (!event) {
        return res.status(404).json({
            success: false,
            message: 'Event not found'
        });
    }
    
    if (event.booked + parseInt(guests) > event.capacity) {
        return res.status(400).json({
            success: false,
            message: 'Not enough capacity for this booking'
        });
    }
    
    event.booked += parseInt(guests);
    
    const booking = {
        id: Date.now(),
        eventId: event.id,
        name,
        email,
        phone,
        guests: parseInt(guests),
        totalPrice: event.price * parseInt(guests),
        status: 'confirmed',
        createdAt: new Date().toISOString()
    };
    
    res.status(201).json({
        success: true,
        message: 'Event booked successfully',
        data: booking
    });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'Server is running',
        timestamp: new Date().toISOString()
    });
});

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        success: false,
        message: 'Something went wrong!'
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: 'Route not found'
    });
});

// Initialize data and start server
initializeData();

app.listen(PORT, () => {
    console.log(`🍽️  Bella Vista Restaurant Server running on port ${PORT}`);
    console.log(`📱 Frontend: http://localhost:${PORT}`);
    console.log(`🔗 API: http://localhost:${PORT}/api`);
});

module.exports = app;
