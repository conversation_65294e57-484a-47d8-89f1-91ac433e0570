/* Custom CSS for Bella Vista Restaurant */

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #D4AF37;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #B8941F;
}

/* Smooth transitions for all elements */
* {
    transition: all 0.3s ease;
}

/* Custom button hover effects */
.btn-primary {
    background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    transform: translateY(0);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

/* Hero section parallax effect */
#home {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

/* Card hover effects */
.card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Menu item animations */
.menu-item {
    opacity: 0;
    transform: translateY(30px);
    animation: slideInUp 0.6s ease forwards;
}

.menu-item:nth-child(1) { animation-delay: 0.1s; }
.menu-item:nth-child(2) { animation-delay: 0.2s; }
.menu-item:nth-child(3) { animation-delay: 0.3s; }
.menu-item:nth-child(4) { animation-delay: 0.4s; }
.menu-item:nth-child(5) { animation-delay: 0.5s; }
.menu-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form focus effects */
input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #D4AF37;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

/* Custom gold gradient text */
.text-gradient {
    background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Elegant dividers */
.divider {
    position: relative;
    text-align: center;
    margin: 2rem 0;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, #D4AF37, transparent);
}

.divider span {
    background: white;
    padding: 0 1rem;
    color: #D4AF37;
    font-weight: 500;
}

/* Loading animation for forms */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Image overlay effects */
.image-overlay {
    position: relative;
    overflow: hidden;
}

.image-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(212, 175, 55, 0.8), rgba(184, 148, 31, 0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.image-overlay:hover::before {
    opacity: 1;
}

.image-overlay img {
    transition: transform 0.3s ease;
}

.image-overlay:hover img {
    transform: scale(1.1);
}

/* Typography enhancements */
.font-serif {
    letter-spacing: -0.025em;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Utility classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease forwards;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .hero-text {
        font-size: 2.5rem;
    }
    
    .card-hover:hover {
        transform: none;
    }
    
    #home {
        background-attachment: scroll;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .text-gold {
        color: #000000 !important;
    }
    
    .bg-gold {
        background-color: #000000 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus visible for accessibility */
*:focus-visible {
    outline: 2px solid #D4AF37;
    outline-offset: 2px;
}
