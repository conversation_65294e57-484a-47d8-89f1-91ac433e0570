// API Client for Bella Vista Restaurant
class RestaurantAPI {
    constructor() {
        this.baseURL = window.location.origin + '/api';
        this.headers = {
            'Content-Type': 'application/json',
        };
    }

    // Generic API request method
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: this.headers,
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }

    // Menu API methods
    async getMenu(category = 'all') {
        const endpoint = category === 'all' ? '/menu' : `/menu?category=${category}`;
        return this.request(endpoint);
    }

    async getMenuItem(id) {
        return this.request(`/menu/${id}`);
    }

    // Reservation API methods
    async createReservation(reservationData) {
        return this.request('/reservations', {
            method: 'POST',
            body: JSON.stringify(reservationData)
        });
    }

    async getReservations() {
        return this.request('/reservations');
    }

    async updateReservation(id, updateData) {
        return this.request(`/reservations/${id}`, {
            method: 'PATCH',
            body: JSON.stringify(updateData)
        });
    }

    // Contact API methods
    async sendContactMessage(messageData) {
        return this.request('/contact', {
            method: 'POST',
            body: JSON.stringify(messageData)
        });
    }

    async getContactMessages() {
        return this.request('/contact');
    }

    // Events API methods
    async getEvents() {
        return this.request('/events');
    }

    async bookEvent(eventId, bookingData) {
        return this.request(`/events/${eventId}/book`, {
            method: 'POST',
            body: JSON.stringify(bookingData)
        });
    }

    // Health check
    async healthCheck() {
        return this.request('/health');
    }
}

// Create global API instance
window.restaurantAPI = new RestaurantAPI();

// Utility functions for API responses
window.APIUtils = {
    // Show loading state
    showLoading(element) {
        if (element) {
            element.disabled = true;
            element.classList.add('loading');
            const originalText = element.textContent;
            element.setAttribute('data-original-text', originalText);
            element.textContent = 'Loading...';
        }
    },

    // Hide loading state
    hideLoading(element) {
        if (element) {
            element.disabled = false;
            element.classList.remove('loading');
            const originalText = element.getAttribute('data-original-text');
            if (originalText) {
                element.textContent = originalText;
                element.removeAttribute('data-original-text');
            }
        }
    },

    // Format currency
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 0
        }).format(amount);
    },

    // Format date
    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    },

    // Format time
    formatTime(timeString) {
        return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-IN', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    },

    // Validate email
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Validate phone number (Indian format)
    isValidPhone(phone) {
        const phoneRegex = /^(\+91|91)?[6-9]\d{9}$/;
        return phoneRegex.test(phone.replace(/\s+/g, ''));
    },

    // Show notification with API response
    showAPINotification(response, type = 'success') {
        const message = response.message || (type === 'success' ? 'Operation completed successfully' : 'An error occurred');
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    },

    // Handle API errors
    handleAPIError(error) {
        console.error('API Error:', error);
        const message = error.message || 'An unexpected error occurred. Please try again.';
        if (typeof showNotification === 'function') {
            showNotification(message, 'error');
        } else {
            alert(message);
        }
    },

    // Debounce function for API calls
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Initialize API connection check
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await window.restaurantAPI.healthCheck();
        console.log('✅ API connection established');
    } catch (error) {
        console.warn('⚠️ API connection failed, using fallback mode');
        // You can implement fallback functionality here
    }
});
