{"name": "bella-vista-restaurant", "version": "1.0.0", "description": "Premium restaurant website with backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["restaurant", "nodejs", "express", "api", "reservations"], "author": "Bella Vista Restaurant", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "nodemailer": "^6.9.7", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "mongoose": "^7.6.3", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}