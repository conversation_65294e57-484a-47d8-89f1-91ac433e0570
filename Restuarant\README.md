# Bella Vista Restaurant Website

A premium, full-stack restaurant website with Node.js backend, featuring elegant design, interactive menus, reservation system, and admin dashboard.

## Features

### 🍽️ **Menu System**
- Dynamic menu loading from backend API
- Interactive filtering by categories (Appetizers, Main Courses, Desserts, Beverages)
- Real-time price updates in Indian Rupees (₹)
- Dietary indicators and special tags
- Smooth animations and responsive design

### 📅 **Reservation System**
- Complete booking form with validation
- Backend API integration for data persistence
- Date/time selection with future date validation
- Party size selection and special requests
- Admin dashboard for reservation management

### 🎉 **Events & Special Occasions**
- Wine tasting events
- Private dining options
- Cooking masterclasses
- Event booking with capacity management

### 👨‍💼 **Admin Dashboard**
- Real-time reservation management
- Contact message handling
- Status updates and notifications
- Statistics and analytics
- Responsive admin interface

### 🔧 **Backend API**
- RESTful API with Express.js
- In-memory data storage (easily upgradeable to database)
- Rate limiting and security middleware
- CORS support for cross-origin requests
- Comprehensive error handling

### 📱 **Responsive Design**
- Mobile-first approach
- Elegant navigation with mobile menu
- Optimized for all screen sizes
- Touch-friendly interactions

## File Structure

```
Restuarant/
├── Frontend Files
│   ├── index.html          # Main website
│   ├── admin.html          # Admin dashboard
│   ├── script.js           # Frontend JavaScript
│   ├── admin.js            # Admin dashboard JS
│   ├── api.js              # API client
│   └── styles.css          # Custom CSS styles
├── Backend Files
│   ├── server.js           # Express.js server
│   ├── package.json        # Node.js dependencies
│   └── .env                # Environment variables
└── README.md               # Documentation
```

## Technologies Used

### Frontend
- **HTML5** - Semantic markup
- **Tailwind CSS** - Utility-first CSS framework
- **Vanilla JavaScript** - Interactive functionality
- **Font Awesome** - Icons
- **Google Fonts** - Typography (Playfair Display, Inter)

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **CORS** - Cross-origin resource sharing
- **Helmet** - Security middleware
- **Express Rate Limit** - API rate limiting
- **Body Parser** - Request parsing

## Setup Instructions

### 1. Install Dependencies
```bash
cd Restuarant
npm install
```

### 2. Environment Setup
Copy `.env` file and update configuration as needed:
```bash
cp .env.example .env
```

### 3. Start the Server
```bash
# Development mode with auto-restart
npm run dev

# Production mode
npm start
```

### 4. Access the Application
- **Website**: http://localhost:3000
- **Admin Dashboard**: http://localhost:3000/admin.html
- **API Health Check**: http://localhost:3000/api/health

## API Endpoints

### Menu
- `GET /api/menu` - Get all menu items
- `GET /api/menu?category=appetizers` - Get menu items by category
- `GET /api/menu/:id` - Get single menu item

### Reservations
- `POST /api/reservations` - Create new reservation
- `GET /api/reservations` - Get all reservations (admin)
- `PATCH /api/reservations/:id` - Update reservation status

### Contact
- `POST /api/contact` - Send contact message
- `GET /api/contact` - Get all messages (admin)

### Events
- `GET /api/events` - Get all events
- `POST /api/events/:id/book` - Book an event

### System
- `GET /api/health` - Health check endpoint

## API Request Examples

### Create Reservation
```javascript
POST /api/reservations
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+91 **********",
  "guests": 4,
  "date": "2024-12-25",
  "time": "19:30",
  "specialRequests": "Anniversary dinner"
}
```

### Send Contact Message
```javascript
POST /api/contact
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "subject": "Catering Inquiry",
  "message": "I would like to inquire about catering services..."
}
```

## Customization

### Colors
The website uses a sophisticated color palette:
- **Gold**: `#D4AF37` (primary accent)
- **Dark Gold**: `#B8941F` (hover states)
- **Cream**: `#FDF6E3` (background)
- **Charcoal**: `#2C2C2C` (text)

### Menu Items
To add/edit menu items, modify the menu section in `index.html`:

```html
<div class="menu-item bg-white p-6 rounded-lg shadow-sm" data-category="appetizers">
    <div class="flex justify-between items-start mb-3">
        <h3 class="text-xl font-serif font-semibold text-charcoal">Dish Name</h3>
        <span class="text-gold font-bold text-lg">$Price</span>
    </div>
    <p class="text-gray-600 mb-2">Description</p>
    <div class="flex space-x-2">
        <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Dietary Tag</span>
    </div>
</div>
```

### Contact Information
Update contact details in the contact section and footer of `index.html`.

## JavaScript Functionality

### Menu Filtering
```javascript
// Filters menu items by category
menuFilters.forEach(filter => {
    filter.addEventListener('click', function() {
        const category = this.getAttribute('data-category');
        // Filter logic
    });
});
```

### Form Handling
- Reservation form with validation
- Contact form with validation
- Local storage for data persistence
- Custom notification system

### Animations
- Scroll-triggered animations using Intersection Observer
- Smooth scrolling navigation
- Hover effects and transitions

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## Performance Features

- Optimized images from Unsplash
- Minimal JavaScript bundle
- CSS animations for smooth interactions
- Lazy loading ready
- SEO-friendly markup

## Accessibility Features

- Semantic HTML structure
- ARIA labels where needed
- Keyboard navigation support
- Focus indicators
- High contrast mode support
- Reduced motion support

## Future Enhancements

- Backend integration for reservations
- Payment processing
- Email notifications
- Admin dashboard
- Multi-language support
- Online ordering system

## License

This project is open source and available under the [MIT License](LICENSE).

## Credits

- Images: [Unsplash](https://unsplash.com)
- Icons: [Font Awesome](https://fontawesome.com)
- Fonts: [Google Fonts](https://fonts.google.com)
- CSS Framework: [Tailwind CSS](https://tailwindcss.com)

---

**Bella Vista Restaurant** - Where culinary artistry meets timeless elegance.
