# Bella Vista Restaurant Website

A premium, responsive restaurant website built with Tailwind CSS and vanilla JavaScript. Features elegant design, interactive menus, reservation system, and event management.

## Features

### 🍽️ **Menu System**
- Interactive menu filtering by categories (Appetizers, Main Courses, Desserts, Beverages)
- Elegant dish presentation with pricing and dietary indicators
- Smooth animations and hover effects
- Responsive grid layout

### 📅 **Reservation System**
- Complete booking form with date/time selection
- Party size selection and special requests
- Form validation and confirmation
- Local storage for reservation data
- Future date validation

### 🎉 **Events & Special Occasions**
- Wine tasting events
- Private dining options
- Cooking masterclasses
- Event booking capabilities

### 📱 **Responsive Design**
- Mobile-first approach
- Elegant navigation with mobile menu
- Optimized for all screen sizes
- Touch-friendly interactions

### ✨ **Premium Features**
- Smooth scroll navigation
- Intersection Observer animations
- Custom notification system
- Professional typography (Playfair Display + Inter)
- Gold accent color scheme
- Accessibility features

## File Structure

```
Restuarant/
├── index.html          # Main HTML file
├── script.js           # JavaScript functionality
├── styles.css          # Custom CSS styles
└── README.md           # Documentation
```

## Technologies Used

- **HTML5** - Semantic markup
- **Tailwind CSS** - Utility-first CSS framework
- **Vanilla JavaScript** - Interactive functionality
- **Font Awesome** - Icons
- **Google Fonts** - Typography (Playfair Display, Inter)

## Setup Instructions

1. **Clone or download** the restaurant folder
2. **Open index.html** in a web browser
3. **No build process required** - everything runs in the browser

## Customization

### Colors
The website uses a sophisticated color palette:
- **Gold**: `#D4AF37` (primary accent)
- **Dark Gold**: `#B8941F` (hover states)
- **Cream**: `#FDF6E3` (background)
- **Charcoal**: `#2C2C2C` (text)

### Menu Items
To add/edit menu items, modify the menu section in `index.html`:

```html
<div class="menu-item bg-white p-6 rounded-lg shadow-sm" data-category="appetizers">
    <div class="flex justify-between items-start mb-3">
        <h3 class="text-xl font-serif font-semibold text-charcoal">Dish Name</h3>
        <span class="text-gold font-bold text-lg">$Price</span>
    </div>
    <p class="text-gray-600 mb-2">Description</p>
    <div class="flex space-x-2">
        <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Dietary Tag</span>
    </div>
</div>
```

### Contact Information
Update contact details in the contact section and footer of `index.html`.

## JavaScript Functionality

### Menu Filtering
```javascript
// Filters menu items by category
menuFilters.forEach(filter => {
    filter.addEventListener('click', function() {
        const category = this.getAttribute('data-category');
        // Filter logic
    });
});
```

### Form Handling
- Reservation form with validation
- Contact form with validation
- Local storage for data persistence
- Custom notification system

### Animations
- Scroll-triggered animations using Intersection Observer
- Smooth scrolling navigation
- Hover effects and transitions

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## Performance Features

- Optimized images from Unsplash
- Minimal JavaScript bundle
- CSS animations for smooth interactions
- Lazy loading ready
- SEO-friendly markup

## Accessibility Features

- Semantic HTML structure
- ARIA labels where needed
- Keyboard navigation support
- Focus indicators
- High contrast mode support
- Reduced motion support

## Future Enhancements

- Backend integration for reservations
- Payment processing
- Email notifications
- Admin dashboard
- Multi-language support
- Online ordering system

## License

This project is open source and available under the [MIT License](LICENSE).

## Credits

- Images: [Unsplash](https://unsplash.com)
- Icons: [Font Awesome](https://fontawesome.com)
- Fonts: [Google Fonts](https://fonts.google.com)
- CSS Framework: [Tailwind CSS](https://tailwindcss.com)

---

**Bella Vista Restaurant** - Where culinary artistry meets timeless elegance.
