// Admin Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    let reservations = [];
    let messages = [];

    // Tab functionality
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            
            // Update active tab button
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'border-gold', 'text-gold');
                btn.classList.add('border-transparent', 'text-gray-500');
            });
            this.classList.add('active', 'border-gold', 'text-gold');
            this.classList.remove('border-transparent', 'text-gray-500');
            
            // Show/hide tab content
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });
            document.getElementById(`${tabName}-tab`).classList.remove('hidden');
        });
    });

    // Load dashboard data
    async function loadDashboardData() {
        try {
            // Check API health
            await restaurantAPI.healthCheck();
            document.getElementById('admin-status').textContent = 'Connected';
            document.getElementById('admin-status').className = 'text-sm text-green-400';
            
            // Load reservations and messages
            await Promise.all([loadReservations(), loadMessages()]);
            updateStats();
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            document.getElementById('admin-status').textContent = 'Offline';
            document.getElementById('admin-status').className = 'text-sm text-red-400';
        }
    }

    // Load reservations
    async function loadReservations() {
        try {
            const response = await restaurantAPI.getReservations();
            reservations = response.data;
            renderReservations();
        } catch (error) {
            console.error('Failed to load reservations:', error);
            showNotification('Failed to load reservations', 'error');
        }
    }

    // Load messages
    async function loadMessages() {
        try {
            const response = await restaurantAPI.getContactMessages();
            messages = response.data;
            renderMessages();
        } catch (error) {
            console.error('Failed to load messages:', error);
            showNotification('Failed to load messages', 'error');
        }
    }

    // Update statistics
    function updateStats() {
        const totalReservations = reservations.length;
        const confirmedReservations = reservations.filter(r => r.status === 'confirmed').length;
        const pendingReservations = reservations.filter(r => r.status === 'pending').length;
        const totalMessages = messages.length;

        document.getElementById('total-reservations').textContent = totalReservations;
        document.getElementById('confirmed-reservations').textContent = confirmedReservations;
        document.getElementById('pending-reservations').textContent = pendingReservations;
        document.getElementById('total-messages').textContent = totalMessages;
    }

    // Render reservations table
    function renderReservations() {
        const tableBody = document.getElementById('reservations-table');
        
        if (reservations.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">No reservations found</td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = reservations.map(reservation => `
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">${reservation.name}</div>
                    <div class="text-sm text-gray-500">${reservation.email}</div>
                    <div class="text-sm text-gray-500">${reservation.phone}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">${APIUtils.formatDate(reservation.date)}</div>
                    <div class="text-sm text-gray-500">${APIUtils.formatTime(reservation.time)}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${reservation.guests} ${reservation.guests === 1 ? 'guest' : 'guests'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(reservation.status)}">
                        ${reservation.status}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <select onchange="updateReservationStatus(${reservation.id}, this.value)" 
                            class="text-sm border border-gray-300 rounded px-2 py-1">
                        <option value="pending" ${reservation.status === 'pending' ? 'selected' : ''}>Pending</option>
                        <option value="confirmed" ${reservation.status === 'confirmed' ? 'selected' : ''}>Confirmed</option>
                        <option value="cancelled" ${reservation.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                        <option value="completed" ${reservation.status === 'completed' ? 'selected' : ''}>Completed</option>
                    </select>
                    ${reservation.specialRequests ? `
                        <button onclick="showSpecialRequests('${reservation.specialRequests}')" 
                                class="ml-2 text-gold hover:text-dark-gold">
                            <i class="fas fa-info-circle"></i>
                        </button>
                    ` : ''}
                </td>
            </tr>
        `).join('');
    }

    // Render messages
    function renderMessages() {
        const container = document.getElementById('messages-container');
        
        if (messages.length === 0) {
            container.innerHTML = `
                <div class="text-center text-gray-500 py-8">No messages found</div>
            `;
            return;
        }

        container.innerHTML = messages.map(message => `
            <div class="bg-gray-50 rounded-lg p-4 border-l-4 ${message.status === 'unread' ? 'border-gold' : 'border-gray-300'}">
                <div class="flex justify-between items-start mb-2">
                    <div>
                        <h3 class="font-semibold text-gray-900">${message.name}</h3>
                        <p class="text-sm text-gray-600">${message.email}</p>
                    </div>
                    <div class="text-right">
                        <span class="text-xs text-gray-500">${new Date(message.createdAt).toLocaleDateString()}</span>
                        ${message.status === 'unread' ? '<span class="ml-2 px-2 py-1 bg-gold text-white text-xs rounded">New</span>' : ''}
                    </div>
                </div>
                <h4 class="font-medium text-gray-800 mb-2">${message.subject}</h4>
                <p class="text-gray-700 text-sm">${message.message}</p>
            </div>
        `).join('');
    }

    // Get status color classes
    function getStatusColor(status) {
        switch (status) {
            case 'confirmed':
                return 'bg-green-100 text-green-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            case 'completed':
                return 'bg-blue-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    // Update reservation status
    window.updateReservationStatus = async function(reservationId, newStatus) {
        try {
            await restaurantAPI.updateReservation(reservationId, { status: newStatus });
            showNotification('Reservation status updated successfully', 'success');
            await loadReservations();
            updateStats();
        } catch (error) {
            console.error('Failed to update reservation:', error);
            showNotification('Failed to update reservation status', 'error');
        }
    };

    // Show special requests
    window.showSpecialRequests = function(requests) {
        alert(`Special Requests:\n\n${requests}`);
    };

    // Refresh buttons
    document.getElementById('refresh-reservations').addEventListener('click', loadReservations);
    document.getElementById('refresh-messages').addEventListener('click', loadMessages);

    // Notification function
    function showNotification(message, type = 'info') {
        const container = document.getElementById('notification-container');
        const notification = document.createElement('div');
        
        const colors = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            info: 'bg-blue-500'
        };
        
        notification.className = `${colors[type]} text-white px-4 py-2 rounded-lg shadow-lg mb-2 transform transition-all duration-300 translate-x-full`;
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        container.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 5000);
    }

    // Auto-refresh every 30 seconds
    setInterval(() => {
        loadDashboardData();
    }, 30000);

    // Initial load
    loadDashboardData();
});
